# rutx14/client.py

import sys
import hashlib
import requests
from pathlib import Path

import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def verify_firmware_file(config):
    logger.info(f"verifying local firmware file path={config.file}")

    firmware_file = Path(config.file)
    if not firmware_file.is_file():

        logger.info(f"firmware missing, downloading '{config.url}'")

        try:
            resp = requests.get(config.url, stream=True)
            resp.raise_for_status()
        except Exception as e:
            logger.error(f"firmware missing and download failed: {e}")
            sys.exit(1)

        with open(config.file, mode="wb") as file:
            for chunk in resp.iter_content(chunk_size=1024 * 16):
                file.write(chunk)
                print("#", end="", flush=True)
            print("done")

        resp.close()

    with open(config.file, "rb") as firmware_file:

        data = firmware_file.read()
        md5 = hashlib.md5(data).hexdigest()
        if md5 != config.md5:
            logger.error(
                f"firmware failed verification, expected='{config.md5}' got='{md5}'"
            )
            sys.exit(1)
