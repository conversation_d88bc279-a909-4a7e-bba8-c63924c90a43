#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import stat
import sys
import time
import json

import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

from . import utils
from . import client
from . import router


def main():
    start = time.time()

    general_settings = utils.config._sections.general
    wireless_settings = utils.config._sections.wireless
    factory_settings = utils.config._sections.factory_settings
    device_settings = utils.config._sections.device
    firmware_settings = utils.config._sections.firmware

    ################################################################################

    logger.info(f"attempting login to factory_settings device={factory_settings}")
    r = router.Router(factory_settings)
    # r = router.Router(device_settings)
    r.login()
    # logger.debug(f'device token={r.token}')

    stat = r.get("system/device/status")
    serial = stat["data"]["mnfinfo"]["serial"]
    if serial != device_settings.serial:
        logger.error(f"wrong device, expected={device_settings.serial} got {serial}")

    stat_file = f"device-{serial}.json"
    with open(stat_file, "w") as file:
        file.write(json.dumps(stat, indent=2))
        logger.info(f"wrote device status to {stat_file}")
    # print(json.dumps(stat, indent=2))

    ################################################################################

    fw_name = stat["data"]["static"]["fw_version"]
    if (fw_name) < firmware_settings.name:

        logger.warning(
            f"upgrade required, expected={firmware_settings.name}, got={fw_name}"
        )

        client.verify_firmware_file(firmware_settings)

        logger.info(f"uploading new firmware")
        fw = r.upload_firmware(firmware_settings.file)
        print(fw)

        data = {"data": {"keep_settings": "1"}}
        fw = r.post("firmware/actions/upgrade", data=data, timeout=(60, 60))
        if fw["success"] != True:
            logger.error(f"upgrade failed")
            sys.exit(1)

        logger.info(f"wait for device to be back online and re-run")
        sys.exit(0)

    ################################################################################

    logger.info("changing default password")
    users_config = r.get_config(config="users", sid="config")
    for user in users_config["data"]:
        logger.debug(
            f"user: name={user['username']} id={user['id']} group={user['group']}"
        )
        if user["username"] == "admin":
            admin_id = user["id"]
            admin_group = user["group"]

    data = {
        "data": {
            "group": admin_group,
            "current_password": factory_settings.password,
            "password": device_settings.password,
            "password_confirm": device_settings.password,
            "ssh_enable": "1",
        }
    }
    resp = r.update_config(config="users", sid="config", id=admin_id, data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info("changing device LAN ip")

    device_ip = r.ip.to_tuple()[0]
    device_netmask = r.ip.guess_network().netmask().to_tuple()[0]

    data = {
        "data": {
            "delegate": "1",
            "enabled": "1",
            "force_link": "1",
            "fwzone": "lan",
            "ip4table": "",
            "ip6assign": "60",
            "ip6hint": "",
            "ip6ifaceid": "",
            "ipaddr": device_ip,
            "macaddr": "",
            "metric": "0",
            "mtu": "",
            "name": "lan",
            "netmask": device_netmask,
            "proto": "static",
            "wan_as_lan": "1",
        }
    }
    resp = r.update(path="interfaces/config/lan", data=data)
    print(json.dumps(resp, indent=2))
    if resp["success"] != True:
        logger.error(f"ip change error")
        sys.exit(1)

    ################################################################################

    logger.info(f"device rebooting, wait for device to be back online ~90 seconds")
    r.reboot()
    time.sleep(90)

    ################################################################################

    logger.info(
        "login to new ip, make sure your host is configured with additional ip address for the interface"
    )

    r = router.Router(device_settings)
    r.login()

    ################################################################################

    logger.info(f"disabling rms config")

    rms_config = r.get_config(config="rms", sid="config")
    print(json.dumps(rms_config, indent=2))
    data = {
        "data": {"enable": "0", "remote": "lan.local", "port": "666", "auth_code": ""}
    }
    resp = r.update_config(config="rms", sid="config", id="rms_connect_mqtt", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info(f"allowing admin login using ssh key")

    pubkey = os.path.expanduser(os.path.expandvars(general_settings.ssh_pubkey_file))
    logger.info(f"reading ssh pubkey file path={pubkey}")
    with open(pubkey, "r") as file:
        pubkey = file.readlines()
        pubkey = pubkey[0].strip(" \r\n")
        logger.info(f"allowing ssh with key={pubkey}")
    data = {
        "data": {
            "enabled": "1",
            "wan_access": "0",
            "port": "22",
            "enable_key_ssh": "2",
            "ssh_keys": pubkey,
        }
    }
    resp = r.update(path="access_control/ssh/config/general", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info(f"disabling bluetooth")

    bt_config = r.get(path="bluetooth/config")
    data = {"data": {"enabled": "0"}}
    resp = r.update(path="bluetooth/config/general", data=data)
    print(json.dumps(resp, indent=2))

    logger.info(f"deleting old ntp_servers")
    time_servers = r.get(path="date_time/ntp/time_servers/config")
    for time_server in time_servers["data"]:
        logger.warning(f"deleting time server {time_server}")
        resp = r.delete(path=f"date_time/ntp/time_servers/config/{time_server['id']}")
        print(json.dumps(resp, indent=2))
    data = {"data": {"hostname": general_settings.ntp_server}}
    logger.info(f"adding config ntp_server")
    resp = r.post(path="date_time/ntp/time_servers/config", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info(f"current dns config")
    dns_config = r.get(path=f"dns/config")
    print(json.dumps(dns_config, indent=2))

    logger.info(f"modifying dns config")
    data = {
        "data": {
            "logqueries": "0",
            "server": ["*******", "*******"],
            "address": [],
            "rebind_protection": "1",
            "strictorder": "0",
            "localservice": "0",
            "interface": [],
            "notinterface": [],
            "boguspriv": "1",
            "localise_queries": "1",
            "serversfile": "",
            "cachesize": "",
        }
    }
    resp = r.update(path=f"dns/config/general", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    for ipv in ["ipv4", "ipv6"]:
        logger.info(f"disabling {ipv} dhcp servers")
        servers = r.get(path=f"dhcp/servers/{ipv}/config")
        print(json.dumps(servers, indent=2))
        for server in servers["data"]:
            logger.warning(f"deleting dhcp {ipv} server {server}")
            resp = r.delete(path=f"dhcp/servers/{ipv}/config/{server['id']}")
            print(json.dumps(resp, indent=2))

    logger.info(f"disabling tracking")
    data = {
        "data": {
            "enabled": "0",
            "track_domain": "lan.local",
            "interval": "6000",
            "track_ipv6": "",
            "track_ipv4": "127.0.0.1",
        }
    }
    resp = r.update(path="internet_connection/global", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info(f"getting current wireless devices config")
    wireless_config = r.get(path="wireless/devices/config")
    print(json.dumps(wireless_config, indent=2))

    logger.info(f"modifying current wireless devices config, radio0=2.4, radio1=5")
    data = {
        "data": [
            {"enabled": "0", "id": "radio0", "txpower": "5"},
            {
                "enabled": "1",
                "id": "radio1",
                "country": wireless_settings.country,
                "txpower": wireless_settings.txpower,
                "channel": wireless_settings.channel,
                "distance": wireless_settings.distance,
            },
        ],
    }
    resp = r.update(path=f"wireless/devices/config", data=data)
    print(json.dumps(resp, indent=2))

    ################################################################################

    logger.info(f"getting current wireless interfaces config")
    interface_config = r.get(path="wireless/interfaces/config")
    for ifconfig in interface_config["data"]:
        ifconfig_id = ifconfig["id"]
        logger.warning(f"deleting wireless interface config {ifconfig}")
        resp = r.delete(path=f"wireless/interfaces/config/{ifconfig_id}")
        print(json.dumps(resp, indent=2))

    ################################################################################

    print(f"So Long, and Thanks for All the Fish. elapsed={time.time() - start:.10f}")
    sys.exit(0)


################################################################################


if __name__ == "__main__":
    main()
