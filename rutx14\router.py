# rutx14/router.py

import os
import time

import requests
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import ipcalc


class Router:
    def __init__(self, config) -> None:

        self.ip = ipcalc.IP(config.ip)
        self.base_url = f"{config.scheme}://{self.ip.to_tuple()[0]}"
        self.api_url = f"{self.base_url}/api"
        self.username = config.username
        self.password = config.password
        self.token = None

    def login(self):
        credentials = {"username": self.username, "password": self.password}

        response = requests.post(
            url=self.api_url + "/login",
            json=credentials,
            verify=False,
            timeout=(10, 10),
        )
        response.raise_for_status()

        if response.status_code == 200:
            data = response.json()
            self.token = data["data"]["token"]
            return data["data"]

    def logout(self):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/logout"

        response = requests.post(url, headers=headers, verify=False, timeout=(10, 10))
        response.raise_for_status()

        if response.status_code == 200:
            response_data = response.json()
            return response_data

    def reboot(self):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/system/actions/reboot"

        try:
            response = requests.post(url=url, headers=headers, verify=False)
            response.raise_for_status()

        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def get(self, path="", use_base_url=False):
        headers = {"Authorization": f"Bearer {self.token}"}
        if use_base_url:
            url = f"{self.base_url}/{path}"
        else:
            url = f"{self.api_url}/{path}"
        try:
            response = requests.get(
                url, headers=headers, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def get_config(self, config, sid="config", id=""):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{config}/config/{id}"
        try:
            response = requests.get(
                url, headers=headers, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def post(self, path="", data={}, timeout=(10, 10)):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{path}"
        try:
            response = requests.post(
                url, headers=headers, json=data, verify=False, timeout=timeout
            )
            response.raise_for_status()

            response_data = response.json()
            return response_data
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def post_config(self, config, sid="config", id="", data={}):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{config}/{sid}"
        try:
            response = requests.post(
                url, headers=headers, json=data, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            response_data = response.json()
            return response_data
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def update(self, path="", data={}):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{path}"
        try:
            response = requests.put(
                url, headers=headers, json=data, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def update_config(self, config, sid="config", id="", data={}):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{config}/{sid}/{id}"

        try:
            response = requests.put(
                url, headers=headers, json=data, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def delete(self, path="", data={}):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{path}"
        try:
            response = requests.delete(
                url, headers=headers, json=data, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def delete_config(self, config, sid="config", id=""):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{config}/{sid}/{id}"
        try:
            response = requests.delete(
                url, headers=headers, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()

            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def generate_backup(self, data):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/backup/actions/generate"
        try:
            response = requests.post(
                url, headers=headers, json=data, verify=False, timeout=(10, 10)
            )
            response.raise_for_status()
            if response.status_code == 200:
                response_data = response.json()
                return response_data
            else:
                return f"Unexpected status code: {response.status_code}"
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    def perform_action(self, config, action, filename="", data={}):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/{config}/actions/{action}"
        try:
            curr_dir = os.getcwd()
            path = os.path.join(curr_dir, filename)
            with open(path, "wb") as file:
                response = requests.post(
                    url=url, headers=headers, json=data, stream=True, verify=False
                )
                response.raise_for_status()

                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
                return "File downloaded successfully."
        except requests.exceptions.RequestException as e:
            return f"Error: {e} - {e.response.text}"

    # curl -k -X POST -H "Authorization: Bearer 111111" -F 'file=@RUTX_R_00.07.15.2_WEBUI.bin' https://192.168.10.11/api/firmware/actions/upload_device_firmware
    # {"success":true,"data":{"valid":"1","fw_version":"RUTX_R_00.07.15.2","sha256":"ba670c6311f12f26916f5e44078af2662715984b1f27492aa6c9311b82b8dcc7","passwd_warning":"0","message_code":"0","authorized":"1","md5":"e66096a01f8577beeb11f7aa84782e76","hw_support":"1","allow_backup":"1","newer":"1","size":"26.60 MB"}}

    def upload_firmware(self, file=""):
        headers = {"Authorization": f"Bearer {self.token}"}
        url = f"{self.api_url}/firmware/actions/upload_device_firmware"

        multipart_form_data = {
            "file": (file, open(file, "rb")),
            # 'action': (None, 'store'),
        }

        response = requests.post(
            url, headers=headers, files=multipart_form_data, verify=False
        )
        response.raise_for_status()

        response_data = response.json()
        return response_data
