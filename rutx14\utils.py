# -*- coding: utf-8 -*-

import os
import sys
import re
import json
import io
import logging
import datetime

import argparse
import configparser

from rutx14 import __version__

NOTSET = "notset"
ENCODING = "utf-8"


def utcnow():
    return datetime.datetime.now(datetime.UTC)


def parse_args() -> object:
    parser = argparse.ArgumentParser(
        prog="rutx14",
        description="rutx14 description",
        epilog="rutx14 epilog",
    )

    # nargs="?",
    parser.add_argument(
        "-c",
        "--config_file",
        # required=True,
        default=os.path.dirname(os.path.realpath(__file__)) + "/config.ini",
        help="Config File",
    )

    parser.add_argument(
        "-V",
        "--version",
        action="version",
        version="%(prog)s {version}".format(version=__version__),
    )

    return parser.parse_args()


args = parse_args()


class AttrDict(dict):
    def __init__(self, *args, **kwargs):
        super(AttrDict, self).__init__(*args, **kwargs)
        self.__dict__ = self


config_abspath = os.path.abspath(
    os.path.expanduser(os.path.expandvars(args.config_file))
)

config = configparser.ConfigParser(dict_type=AttrDict)

try:
    print(f"reading config file path={config_abspath}")
    with open(config_abspath, "r") as file:
        config_content = file.read()
    config.read(config_abspath)
    version = config.get("general", "version")
    if version != __version__:
        print(f"unsupported config file: version {version} != {__version__}")
        sys.exit(1)

except FileNotFoundError as e:
    print(f"error reading missing config file: path={args.config_file}")
    sys.exit(1)
except Exception as e:
    print(f"Error reading config file path={args.config_file}: {e}")
    sys.exit(1)
